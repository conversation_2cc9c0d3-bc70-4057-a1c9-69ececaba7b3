<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { couponSave } from "@/api/coupon";

defineOptions({
  name: "CouponManagementCreate"
});

const router = useRouter();

// 表单数据 - 修改为与API匹配的字段名
const formData = ref({
  name: "", // 优惠券名称
  conditionAmount: "", // 满减条件金额
  discountAmount: "", // 优惠金额
  totalIssue: "", // 总发行量
  distributionStartTime: "", // 发放开始时间
  distributionEndTime: "", // 发放结束时间
  isUseLimit: true, // 使用是否有限制
  startTime: "", // 使用开始时间
  endTime: "", // 使用结束时间
  remarks: "", // 备注
  enabled: true // 状态开关
});

// 表单验证规则 - 修改为与API匹配的字段名
const formRules = reactive({
  name: [
    { required: true, message: "请输入优惠券名称", trigger: "blur" }
  ],
  conditionAmount: [
    { required: true, message: "请输入满减条件金额", trigger: "blur" },
    { pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的金额格式", trigger: "blur" }
  ],
  discountAmount: [
    { required: true, message: "请输入优惠金额", trigger: "blur" },
    { pattern: /^\d+(\.\d{1,2})?$/, message: "请输入正确的金额格式", trigger: "blur" }
  ],
  totalIssue: [
    { required: true, message: "请输入总发行量", trigger: "blur" },
    { pattern: /^\d+$/, message: "请输入正整数", trigger: "blur" }
  ],
  distributionEndTime: [
    { required: true, message: "请选择发放结束时间", trigger: "change" }
  ],
  startTime: [
    {
      validator: (rule, value, callback) => {
        if (formData.value.isUseLimit && !value) {
          callback(new Error("请选择使用开始时间"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  endTime: [
    {
      validator: (rule, value, callback) => {
        if (formData.value.isUseLimit && !value) {
          callback(new Error("请选择使用结束时间"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
});

// 使用时间类型选项
const useTimeOptions = [
  { label: "不限", value: false },
  { label: "有限", value: true }
];

// 状态选项
const statusOptions = [
  { label: "启用", value: true },
  { label: "停用", value: false }
];

const formRef = ref();
const loading = ref(false);

// 时间转换为时间戳的工具函数
const convertToTimestamp = (dateValue) => {
  if (!dateValue) return 0;
  return new Date(dateValue).getTime();
};

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      // 验证满减金额逻辑
      if (parseFloat(formData.value.discountAmount) >= parseFloat(formData.value.conditionAmount)) {
        ElMessage.error("优惠金额不能大于或等于满减条件金额");
        return;
      }

      // 验证使用时间范围
      if (formData.value.isUseLimit) {
        if (!formData.value.startTime || !formData.value.endTime) {
          ElMessage.error("请选择使用时间范围");
          return;
        }
        if (new Date(formData.value.startTime) >= new Date(formData.value.endTime)) {
          ElMessage.error("使用开始时间必须早于结束时间");
          return;
        }
      }

      // 验证发放时间范围
      if (formData.value.distributionEndTime) {
        const now = new Date();
        if (new Date(formData.value.distributionEndTime) <= now) {
          // ElMessage.error("发放结束时间必须晚于当前时间");
          // return;
        }
      }

      loading.value = true;

      try {
        // 构建API请求数据
        const requestData = {
          name: formData.value.name,
          couponDiscountType: "FULL_REDUCTION", // 固定传FULL_REDUCTION
          conditionAmount: parseFloat(formData.value.conditionAmount),
          discountAmount: parseFloat(formData.value.discountAmount),
          totalIssue: parseInt(formData.value.totalIssue),
          distributionStartTime: formData.value.distributionStartTime
            ? convertToTimestamp(formData.value.distributionStartTime)
            : Date.now(), // 如果未填写则使用当前时间
          distributionEndTime: convertToTimestamp(formData.value.distributionEndTime),
          isUseLimit: formData.value.isUseLimit,
          remarks: formData.value.remarks || "",
          couponScope: "ALL", // 固定传"ALL"
          enabled: formData.value.enabled
        };

        // 只有当isUseLimit为true时才传递使用时间
        if (formData.value.isUseLimit) {
          requestData.startTime = convertToTimestamp(formData.value.startTime);
          requestData.endTime = convertToTimestamp(formData.value.endTime);
        }

        console.log("提交的API数据:", requestData);

        // 调用API
        const response = await couponSave(requestData, {
          operateLogType: "COUPON_MANAGEMENT",
          operateType: "CREATE",
          operatorTarget: formData.value.name
        });

        ElMessage.success("优惠券创建成功");
        router.push("/coupon/management/index");
      } catch (error) {
        console.error("创建优惠券失败:", error);
        ElMessage.error("创建优惠券失败，请重试");
      } finally {
        loading.value = false;
      }
    } else {
      ElMessage.error("请完善表单信息");
    }
  });
};

// 取消操作
const handleCancel = () => {
  ElMessageBox.confirm(
    "确定要取消新建优惠券吗？未保存的数据将丢失。",
    "确认取消",
    {
      confirmButtonText: "确定",
      cancelButtonText: "继续编辑",
      type: "warning"
    }
  )
    .then(() => {
      router.push("/coupon/management/index");
    })
    .catch(() => {
      // 用户选择继续编辑
    });
};

onMounted(() => {
  // 页面初始化逻辑
});
</script>

<template>
  <div class="coupon-create">
    <div class="common">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="coupon-form"
      >
        <div class="form-section">
          <h3 class="section-title">优惠券基础信息</h3>

          <el-form-item label="优惠券名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入优惠券名称"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="优惠券类型">
            <span>满减券</span>
          </el-form-item>

          <el-form-item label="满" prop="conditionAmount">
            <div class="discount-input-group">
              <el-input
                v-model="formData.conditionAmount"
                placeholder="请输入金额"
                style="width: 150px"
              />
              <span class="form-text">减</span>
              <el-input
                v-model="formData.discountAmount"
                placeholder="请输入金额"
                style="width: 150px"
              />
            </div>
          </el-form-item>

          <el-form-item label="总发行量" prop="totalIssue">
            <el-input
              v-model="formData.totalIssue"
              placeholder="请输入优惠券总发行量"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="发放时间">
            <div class="time-range">
              <span>开始</span>
              <el-date-picker
                v-model="formData.distributionStartTime"
                type="datetime"
                placeholder="请选择开始时间（可选）"
                style="width: 200px; margin: 0 10px"
              />
              <span>结束</span>
              <el-date-picker
                v-model="formData.distributionEndTime"
                type="datetime"
                placeholder="请选择结束时间"
                style="width: 200px; margin-left: 10px"
              />
            </div>
          </el-form-item>

          <el-form-item label="使用时间" prop="isUseLimit">
            <el-radio-group v-model="formData.isUseLimit">
              <el-radio
                v-for="option in useTimeOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            v-if="formData.isUseLimit"
            label=" "
            prop="startTime"
          >
            <div class="time-range">
              <span>开始</span>
              <el-date-picker
                v-model="formData.startTime"
                type="datetime"
                placeholder="请选择开始时间"
                style="width: 200px; margin: 0 10px"
              />
              <span>结束</span>
              <el-date-picker
                v-model="formData.endTime"
                type="datetime"
                placeholder="请选择结束时间"
                style="width: 200px; margin-left: 10px"
              />
            </div>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="4"
              placeholder="请输入备注信息"
              style="width: 400px"
            />
          </el-form-item>

          <el-form-item label="状态" prop="enabled">
            <el-radio-group v-model="formData.enabled">
              <el-radio
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.value"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <div class="form-actions">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            确认
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-create {
  .common {
    height: 88vh;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    position: relative;
  }

  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 20px;
      font-weight: bold;
      color: #606266;
      margin: 0;
    }
  }

  .coupon-form {
    max-width: 100%;
    padding-bottom: 80px;
  }

  .form-section {
    margin-bottom: 32px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  .time-range {
    display: flex;
    align-items: center;

    span {
      color: #606266;
      font-size: 14px;
    }
  }

  .discount-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .form-text {
    margin: 0 10px;
    color: #606266;
    font-size: 14px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    position: absolute;
    bottom: 20px;
    right: 20px;
    left: 20px;
    background-color: #fff;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
  }
}
</style>
